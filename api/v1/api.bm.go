// Code generated by protoc-gen-bm v0.1, DO NOT EDIT.
// source: recommend-base/api/v1/api.proto

/*
Package v1 is a generated blademaster stub package.
This code was generated with kratos/tool/protobuf/protoc-gen-bm v0.1.

package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..

It is generated from these files:

	recommend-base/api/v1/api.proto
*/
package v1

import (
	"context"

	bm "go-common/library/net/http/blademaster"
	"go-common/library/net/http/blademaster/binding"

	google_protobuf1 "github.com/golang/protobuf/ptypes/empty"
)

// to suppressed 'imported but not used warning'
var _ *bm.Context
var _ context.Context
var _ binding.StructValidator

var PathLiveRoomPing = "/maoer.service.recommendbase.v1.LiveRoom/Ping"
var PathLiveRoomGetAllLivingRoomsInfo = "/maoer.service.recommendbase.v1.LiveRoom/GetAllLivingRoomsInfo"

// LiveRoomBMServer is the server API for LiveRoom service.
type LiveRoomBMServer interface {
	Ping(ctx context.Context, req *google_protobuf1.Empty) (resp *google_protobuf1.Empty, err error)

	GetAllLivingRoomsInfo(ctx context.Context, req *LivingRoomListReq) (resp *AllLivingRoomsInfoResp, err error)
}

var v1LiveRoomSvc LiveRoomBMServer

func liveRoomPing(c *bm.Context) {
	p := new(google_protobuf1.Empty)
	if err := c.BindWith(p, binding.Default(c.Request.Method, c.Request.Header.Get("Content-Type"))); err != nil {
		return
	}
	resp, err := v1LiveRoomSvc.Ping(c, p)
	c.JSON(resp, err)
}

func liveRoomGetAllLivingRoomsInfo(c *bm.Context) {
	p := new(LivingRoomListReq)
	if err := c.BindWith(p, binding.Default(c.Request.Method, c.Request.Header.Get("Content-Type"))); err != nil {
		return
	}
	resp, err := v1LiveRoomSvc.GetAllLivingRoomsInfo(c, p)
	c.JSON(resp, err)
}

// RegisterLiveRoomBMServer Register the blademaster route
func RegisterLiveRoomBMServer(e *bm.Engine, server LiveRoomBMServer) {
	v1LiveRoomSvc = server
	e.GET("/maoer.service.recommendbase.v1.LiveRoom/Ping", liveRoomPing)
	e.GET("/maoer.service.recommendbase.v1.LiveRoom/GetAllLivingRoomsInfo", liveRoomGetAllLivingRoomsInfo)
}
