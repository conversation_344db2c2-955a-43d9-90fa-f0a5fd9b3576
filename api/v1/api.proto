// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package maoer.service.recommendbase.v1;

// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "v1";
option (gogoproto.goproto_getters_all) = false;

service LiveRoom {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  rpc GetAllLivingRoomsInfo(LivingRoomListReq) returns (AllLivingRoomsInfoResp);
}

message LivingRoomListReq {
  int64 page = 1 [(gogoproto.moretags) = 'form:"page" validate:"required,min=1"'];
  int64 page_size = 2 [(gogoproto.moretags) = 'form:"page_size" validate:"required,min=1,max=100"'];
}

// 全量在播房间列表响应
message AllLivingRoomsInfoResp {
  // 房间信息数组
  repeated AllLivingRoomInfo list = 1;
  // 分页信息
  Pagination pagination = 2;
}

message Pagination {
  int64 maxpage = 1;
  int64 count = 2;
  int64 p = 3;
  int64 pagesize = 4;
}

message AllLivingRoomInfo {
  // 房间 id
  int64 roomid = 1;
  // 主播 id
  int64 uid = 2;
  // 直播间一级分区 id
  int64 catalog_id = 3;
  // 直播间二级分区 id
  int64 sub_catalog_id = 4;
  // 直播间挂载的个性词条 id
  int64 custom_tag_id = 5;
  // 直播间挂载的个性词条
  string custom_tag_name = 6;
  // 直播间标题
  string room_title = 7;
  // 直播间创建时间（秒级时间戳）
  int64 ctime = 8;
  // 本场直播开始时间（秒级时间戳）
  int64 live_start_time = 9;
  // 实时在线人数（展示）
  int64 online = 10;
  // 总进房
  int64 actually_online = 11;
  // 实时弹幕数（本场累积消息数）
  int64 message_count = 12;
  // 实时热度
  int64 score = 13;
  // 实时上榜人数（本场榜）
  int64 board = 14;
}
