// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package maoer.service.recommendbase.v2;

// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "v2";
option (gogoproto.goproto_getters_all) = false;

service LiveRoom {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  rpc GetAllLivingRoomsInfo(LivingRoomListReq) returns (AllLivingRoomsInfoResp);
}

message LivingRoomListReq {
  int64 page = 1 [(gogoproto.moretags) = 'form:"page" validate:"required,min=1"'];
  int64 page_size = 2 [(gogoproto.moretags) = 'form:"page_size" validate:"required,min=1,max=100"'];
}

// 全量在播房间列表响应
message AllLivingRoomsInfoResp {
  // 房间信息数组
  repeated AllLivingRoomInfo list = 1;
  // 分页信息
  Pagination pagination = 2;
}

message Pagination {
  int64 maxpage = 1;
  int64 count = 2;
  int64 p = 3;
  int64 pagesize = 4;
}

message AllLivingRoomInfo {
  // 房间 id
  int64 roomid = 1;
  // 主播 id
  int64 uid = 2;
  // 直播间一级分区 id
  int64 catalog_id = 3;
  // 直播间二级分区 id
  int64 sub_catalog_id = 4;
  // 直播间挂载的个性词条 id
  int64 custom_tag_id = 5;
  // 直播间挂载的个性词条
  string custom_tag_name = 6;
  // 直播间标题
  string room_title = 7;
  // 直播间创建时间（秒级时间戳）
  int64 ctime = 8;
  // 本场直播开始时间（秒级时间戳）
  int64 live_start_time = 9;
  // 实时在线人数（展示）
  int64 online = 10;
  // 总进房
  int64 actually_online = 11;
  // 实时弹幕数（本场累积消息数）
  int64 message_count = 12;
  // 实时热度
  int64 score = 13;
  // 实时上榜人数（本场榜）
  int64 board = 14;
  // 是否是新星
  bool is_new_star = 15;
}

service Recommend {
  // 获取首页 Feed 干预卡列表
  rpc GetHomeFeedInterventionCards(HomeFeedInterventionCardsReq) returns (InterventionCardsResp);
  // 获取直播页干预卡列表
  rpc GetLivePageInterventionCards(LivePageInterventionCardsReq) returns (InterventionCardsResp);
}

// 首页 Feed 干预卡请求
message HomeFeedInterventionCardsReq {
  int64 page = 1 [(gogoproto.moretags) = 'form:"page" validate:"required,min=1"'];
  int64 page_size = 2 [(gogoproto.moretags) = 'form:"page_size" validate:"required,min=1,max=100"'];
}

// 直播页干预卡请求
message LivePageInterventionCardsReq {
  int64 page = 1 [(gogoproto.moretags) = 'form:"page" validate:"required,min=1"'];
  int64 page_size = 2 [(gogoproto.moretags) = 'form:"page_size" validate:"required,min=1,max=100"'];
}

// 干预卡列表响应
message InterventionCardsResp {
  // 卡片列表
  repeated InterventionCard list = 1;
  // 分页信息
  Pagination pagination = 2;
}

message InterventionCard {
  // 干预卡 ID
  int64 card_id = 1;
  // 干预卡类型，1：剧集卡，2：直播卡
  int64 card_type = 2;
  // 干预卡元素 ID，例如剧集卡是剧集 ID、直播卡是直播间 ID
  int64 element_id = 3;
  // 生效时间，秒级时间戳，包含此时间点（闭区间）
  int64 start_time = 4;
  // 失效时间，秒级时间戳，不包含此时间点（开区间）
  int64 end_time = 5;
  // 目标曝光量
  int64 exposure_target = 6;
  // 当前曝光量
  int64 exposure_current = 7;
  // 分区 id
  int64 catalog_id = 8;
  // 干预来源，0：曝光流量卡，1：公会推荐位直播，2：热门列表固定主播，3：运营推荐主播，4：直播推荐排期
  int64 operate_source = 9;
}
