// Code generated by kratos tool btsgen. DO NOT EDIT.

/*
  Package dao is a generated cache proxy package.
  It is generated from:
  type Dao interface {
		Close()
		Ping(ctx context.Context) (err error)

		// bts: -nullcache=&RoomList{Pagination:&Pagination{Count:-1}} -check_null_code=$!=nil&&$.Pagination.Count==-1
		LivingRoomsInfo(ctx context.Context, page int64, pageSize int64) (*RoomList, error)
		// bts: -nullcache=&RoomExtraInfoList{List:[]*RoomExtraInfo{{RoomID:-1}}} -check_null_code=$!=nil&&len($.List)!=0&&$.List[0].RoomID==-1
		RoomsExtraInfo(ctx context.Context, params RoomsExtraInfoParams) (*RoomExtraInfoList, error)

		// bts: -nullcache=[]*mrecommendedexposurelevel.Model{{ID:-1}} -check_null_code=$!=nil&&len($)!=0&&$[0].ID==-1
		GetRecommendedExposureLevels(ctx context.Context) ([]*mrecommendedexposurelevel.Model, error)
		// bts: -nullcache=&InterventionCardList{List:[]*InterventionCard{{CardID:-1}}} -check_null_code=$!=nil&&len($.List)!=0&&$.List[0].CardID==-1
		GetHomeFeedInterventionCards(ctx context.Context, page int64, pageSize int64) (*InterventionCardList, error)
		// bts: -nullcache=&InterventionCardList{List:[]*InterventionCard{{CardID:-1}}} -check_null_code=$!=nil&&len($.List)!=0&&$.List[0].CardID==-1
		GetLivePageInterventionCards(ctx context.Context, page int64, pageSize int64) (*InterventionCardList, error)
	}
*/

package dao

import (
	"context"

	"go-common/library/cache"
	"recommend-base/internal/model/mrecommendedexposurelevel"
)

// LivingRoomsInfo get data from cache if miss will call source method, then add to cache.
func (d *dao) LivingRoomsInfo(c context.Context, page int64, pageSize int64) (res *RoomList, err error) {
	addCache := true
	res, err = d.CacheLivingRoomsInfo(c, page, pageSize)
	if err != nil {
		addCache = false
		err = nil
	}
	defer func() {
		if res != nil && res.Pagination.Count == -1 {
			res = nil
		}
	}()
	if res != nil {
		cache.MetricHits.Inc("bts:LivingRoomsInfo")
		return
	}
	cache.MetricMisses.Inc("bts:LivingRoomsInfo")
	res, err = d.RawLivingRoomsInfo(c, page, pageSize)
	if err != nil {
		return
	}
	miss := res
	if miss == nil {
		miss = &RoomList{Pagination: &Pagination{Count: -1}}
	}
	if !addCache {
		return
	}
	d.cache.Do(c, func(c context.Context) {
		d.AddCacheLivingRoomsInfo(c, page, miss, pageSize)
	})
	return
}

// RoomsExtraInfo get data from cache if miss will call source method, then add to cache.
func (d *dao) RoomsExtraInfo(c context.Context, params RoomsExtraInfoParams) (res *RoomExtraInfoList, err error) {
	addCache := true
	res, err = d.CacheRoomsExtraInfo(c, params)
	if err != nil {
		addCache = false
		err = nil
	}
	defer func() {
		if res != nil && len(res.List) != 0 && res.List[0].RoomID == -1 {
			res = nil
		}
	}()
	if res != nil {
		cache.MetricHits.Inc("bts:RoomsExtraInfo")
		return
	}
	cache.MetricMisses.Inc("bts:RoomsExtraInfo")
	res, err = d.RawRoomsExtraInfo(c, params)
	if err != nil {
		return
	}
	miss := res
	if miss == nil {
		miss = &RoomExtraInfoList{List: []*RoomExtraInfo{{RoomID: -1}}}
	}
	if !addCache {
		return
	}
	d.cache.Do(c, func(c context.Context) {
		d.AddCacheRoomsExtraInfo(c, params, miss)
	})
	return
}

// GetRecommendedExposureLevels get data from cache if miss will call source method, then add to cache.
func (d *dao) GetRecommendedExposureLevels(c context.Context) (res []*mrecommendedexposurelevel.Model, err error) {
	addCache := true
	res, err = d.CacheGetRecommendedExposureLevels(c)
	if err != nil {
		addCache = false
		err = nil
	}
	defer func() {
		if res != nil && len(res) != 0 && res[0].ID == -1 {
			res = nil
		}
	}()
	if len(res) != 0 {
		cache.MetricHits.Inc("bts:GetRecommendedExposureLevels")
		return
	}
	cache.MetricMisses.Inc("bts:GetRecommendedExposureLevels")
	res, err = d.RawGetRecommendedExposureLevels(c)
	if err != nil {
		return
	}
	var miss = res
	if len(miss) == 0 {
		miss = []*mrecommendedexposurelevel.Model{{ID: -1}}
	}
	if !addCache {
		return
	}
	d.cache.Do(c, func(c context.Context) {
		d.AddCacheGetRecommendedExposureLevels(c, miss)
	})
	return
}

// GetHomeFeedInterventionCards get data from cache if miss will call source method, then add to cache.
func (d *dao) GetHomeFeedInterventionCards(c context.Context, page int64, pageSize int64) (res *InterventionCardList, err error) {
	addCache := true
	res, err = d.CacheGetHomeFeedInterventionCards(c, page, pageSize)
	if err != nil {
		addCache = false
		err = nil
	}
	defer func() {
		if res != nil && len(res.List) != 0 && res.List[0].CardID == -1 {
			res = nil
		}
	}()
	if res != nil {
		cache.MetricHits.Inc("bts:GetHomeFeedInterventionCards")
		return
	}
	cache.MetricMisses.Inc("bts:GetHomeFeedInterventionCards")
	res, err = d.RawGetHomeFeedInterventionCards(c, page, pageSize)
	if err != nil {
		return
	}
	miss := res
	if miss == nil {
		miss = &InterventionCardList{List: []*InterventionCard{{CardID: -1}}}
	}
	if !addCache {
		return
	}
	d.cache.Do(c, func(c context.Context) {
		d.AddCacheGetHomeFeedInterventionCards(c, page, miss, pageSize)
	})
	return
}

// GetLivePageInterventionCards get data from cache if miss will call source method, then add to cache.
func (d *dao) GetLivePageInterventionCards(c context.Context, page int64, pageSize int64) (res *InterventionCardList, err error) {
	addCache := true
	res, err = d.CacheGetLivePageInterventionCards(c, page, pageSize)
	if err != nil {
		addCache = false
		err = nil
	}
	defer func() {
		if res != nil && len(res.List) != 0 && res.List[0].CardID == -1 {
			res = nil
		}
	}()
	if res != nil {
		cache.MetricHits.Inc("bts:GetLivePageInterventionCards")
		return
	}
	cache.MetricMisses.Inc("bts:GetLivePageInterventionCards")
	res, err = d.RawGetLivePageInterventionCards(c, page, pageSize)
	if err != nil {
		return
	}
	miss := res
	if miss == nil {
		miss = &InterventionCardList{List: []*InterventionCard{{CardID: -1}}}
	}
	if !addCache {
		return
	}
	d.cache.Do(c, func(c context.Context) {
		d.AddCacheGetLivePageInterventionCards(c, page, miss, pageSize)
	})
	return
}
