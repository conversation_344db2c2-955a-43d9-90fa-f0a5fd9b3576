package dao

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go-common/library/conf/paladin.v2"
	"go-common/library/log"

	"recommend-base/internal/util/openapi"
)

const apiGetRoomsExtraInfo = "/openapi/rooms-extra-info"

type apiGetRoomsExtraInfoResp struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
	Data    *struct {
		Data []*RoomExtraInfo `json:"data"`
	} `json:"data"`
}

type RoomsExtraInfoParams struct {
	RoomIDs []int64
}

type RoomExtraInfoList struct {
	List []*RoomExtraInfo
}

// RoomExtraInfo room extra info
type RoomExtraInfo struct {
	RoomID         int64  `json:"room_id"`
	CatalogID      int64  `json:"catalog_id"`
	SubCatalogID   int64  `json:"sub_catalog_id"`
	CustomTagID    int64  `json:"custom_tag_id"`
	CustomTagName  string `json:"custom_tag_name"`
	Online         int64  `json:"online"`
	ActuallyOnline int64  `json:"actually_online"`
	MessageCount   int64  `json:"message_count"`
	Board          int64  `json:"board"`
	IsNewStar      bool   `json:"is_new_star"`
}

// MClient http client.
type MClient struct {
	liveOpenApi *openapi.Client
}

// NewMClient new a missevan rpc client.
func NewMClient() (mclient *MClient, cf func(), err error) {
	var (
		cfg           openapi.Config
		ct            paladin.TOML
		openApiClient *openapi.Client
	)
	if err = paladin.Get("mclient.toml").Unmarshal(&ct); err != nil {
		return
	}
	if err = ct.Get("live_openapi").UnmarshalTOML(&cfg); err != nil {
		return
	}

	openApiClient, err = openapi.NewClient(openapi.Config{
		Endpoint:  cfg.Endpoint,
		AppKey:    cfg.AppKey,
		AppSecret: cfg.AppSecret,
		Timeout:   cfg.Timeout,
		KeepAlive: cfg.KeepAlive,
		Dial:      cfg.Dial,
	})
	if err != nil {
		return
	}
	mclient = &MClient{
		liveOpenApi: openApiClient,
	}
	cf = func() {}
	return
}

// RawRoomsExtraInfo get data from mclient
func (d *dao) RawRoomsExtraInfo(ctx context.Context, params RoomsExtraInfoParams) (rooms *RoomExtraInfoList, err error) {
	data := new(apiGetRoomsExtraInfoResp)
	var reqParams = url.Values{}
	reqParams.Add("room_ids", joinInt64Array(params.RoomIDs, ","))
	err = d.mclient.liveOpenApi.Post(ctx, apiGetRoomsExtraInfo, reqParams, data)
	if err != nil {
		log.Error("RawRoomsExtraInfo liveRpc Do error(%v), url(%s)", err, apiGetRoomsExtraInfo)
		return nil, err
	}
	if data.Code != 0 {
		log.Error("RawRoomsExtraInfo liveRpc Do code(%d), message(%s) url(%s)", data.Code, data.Message, apiGetRoomsExtraInfo)
		return nil, fmt.Errorf("code=%d, message=%s", data.Code, data.Message)
	}

	if data.Data == nil || len(data.Data.Data) <= 0 {
		return &RoomExtraInfoList{List: []*RoomExtraInfo{}}, nil
	}

	rooms = &RoomExtraInfoList{List: data.Data.Data}
	return
}

func joinInt64Array(pieces []int64, glue string) string {
	if len(pieces) == 0 {
		return ""
	}
	stringPieces := make([]string, len(pieces))
	for i, v := range pieces {
		stringPieces[i] = strconv.FormatInt(v, 10)
	}
	return strings.Join(stringPieces, glue)
}
