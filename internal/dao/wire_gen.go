// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package dao

// Injectors from wire.go:

//go:generate kratos tool wire
func newTestDao() (*dao, func(), error) {
	redis, cleanup, err := NewRedis()
	if err != nil {
		return nil, nil, err
	}
	mClient, cleanup2, err := NewMClient()
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	db, cleanup3, err := NewDB()
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	daoDao, cleanup4, err := newDao(redis, mClient, db)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	return daoDao, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
