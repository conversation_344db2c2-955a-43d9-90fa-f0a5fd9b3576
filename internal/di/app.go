package di

import (
	"context"
	"time"

	v1 "recommend-base/internal/service/v1"
	v2 "recommend-base/internal/service/v2"

	"go-common/library/log"
	bm "go-common/library/net/http/blademaster"
	"go-common/library/net/rpc/warden"
)

//go:generate kratos tool wire
type App struct {
	svcV1 *v1.Service
	svcV2 *v2.Service
	http  *bm.Engine
	grpc  *warden.Server
}

func NewApp(svcV1 *v1.Service, svcV2 *v2.Service, h *bm.Engine, g *warden.Server) (app *App, closeFunc func(), err error) {
	app = &App{
		svcV1: svcV1,
		svcV2: svcV2,
		http:  h,
		grpc:  g,
	}
	closeFunc = func() {
		ctx, cancel := context.WithTimeout(context.Background(), 35*time.Second)
		if err := g.Shutdown(ctx); err != nil {
			log.Error("grpcSrv.Shutdown error(%v)", err)
		}
		if err := h.Shutdown(ctx); err != nil {
			log.Error("httpSrv.Shutdown error(%v)", err)
		}
		cancel()
	}
	return
}
