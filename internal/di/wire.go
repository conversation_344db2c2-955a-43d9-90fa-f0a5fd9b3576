//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package di

import (
	"recommend-base/internal/dao"
	"recommend-base/internal/server/grpc"
	"recommend-base/internal/server/http"
	v1 "recommend-base/internal/service/v1"
	v2 "recommend-base/internal/service/v2"

	"github.com/google/wire"
)

//go:generate kratos t wire
func InitApp() (*App, func(), error) {
	panic(wire.Build(dao.Provider, v1.Provider, v2.Provider, http.New, grpc.New, NewApp))
}
