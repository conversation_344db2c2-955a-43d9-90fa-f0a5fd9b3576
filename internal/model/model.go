package model

// <PERSON><PERSON>s hello kratos.
type Kratos struct {
	Hello string
}

// 直播房间状态
const (
	LiveStatusHidden int = iota - 1 // 用户注销（搜索隐藏）
	LiveStatusClosed                // 没有开启房间
	LiveStatusOpen                  // 房间开启
)

// Live live model
type Live struct {
	ID            int64
	RoomID        int64
	CatalogID     int64
	Title         string
	Intro         string
	Status        int
	LiveStartTime int64
	ContractID    int64
	CreateTime    int64
	ModifiedTime  int64
	UserID        int64
	Cover         string
	Score         int64
	Username      string
}
