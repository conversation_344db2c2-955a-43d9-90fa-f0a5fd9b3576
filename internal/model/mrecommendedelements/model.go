package mrecommendedelements

import (
	"encoding/json"
)

// Model of m_recommended_elements
type Model struct {
	ID          int64       // ID
	Client      int64       // 平台（0 安卓或 iOS，1 安卓，2 iOS）
	ModuleType  ModuleType  // 模块类型（1 版头图模块、 2 精品必听、活动等四个小图标模块、3 猜你喜欢音模块、4 精品周更模块、5 推荐剧集或音单模块、6 今日推荐音模块）
	ModuleID    int64       // 模块 ID
	ElementType ElementType // 元素类型（0 其它、1 音单、2 剧集、3 单音、4 活动）
	ElementID   int64       // 元素 ID
	Summary     string      // 简介
	Cover       string      // 封面
	URL         string      // 原始链接
	Sort        int64       // 排序值（0、1、2... 越小越靠前，由程序控制）
	CreatorID   int64       // 负责人用户 ID
	StartTime   int64       // 自动上线时间
	EndTime     int64       // 自动下线时间
	Archive     int64       // 是否为历史归档：0 为否， 1 为（即为被删去的）
	CreateTime  int64       // 创建时间
	UpdateTime  int64       // 更新时间
	More        *string     // 更多设置
	MoreInfo    *More       // 更多数据
}

// More 更多设置
type More struct {
	ExposureLevelID *int64 `json:"exposure_level_id"`
}

// ModuleType 模块类型
type ModuleType = int

// ModuleType 模块类型
const (
	ModuleTypeHomeFeed ModuleType = 18 // 首页 Feed 流
)

// ElementType 元素类型
type ElementType = int

// ElementType 元素类型
const (
	ElementTypeOthers      ElementType = iota // 其它
	ElementTypeAlbum                          // 音单
	ElementTypeDrama                          // 剧集
	ElementTypeSound                          // 单音
	ElementTypeEvent                          // 活动
	ElementTypeLive                           // 直播
	ElementTypeSearchWords                    // 搜索词
)

func (m *Model) AfterFind() error {
	if m.More == nil {
		return nil
	}
	var more More
	err := json.Unmarshal([]byte(*m.More), &more)
	if err != nil {
		return err
	}
	m.MoreInfo = &more
	return nil
}
