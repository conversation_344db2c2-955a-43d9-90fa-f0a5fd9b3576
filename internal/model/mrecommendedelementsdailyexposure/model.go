package mrecommendedelementsdailyexposure

// Scene 场景
type Scene = int8

// Scene 场景枚举
const (
	SceneHomePage Scene = 1 // 首页
	SceneLivePage Scene = 2 // 直播页
)

// DailyExposureModel of m_recommended_elements_daily_exposure
type DailyExposureModel struct {
	ID            int64  // 主键
	CreateTime    int64  // 创建时间
	ModifiedTime  int64  // 更新时间
	Bizdate       string // 业务日期
	Scene         Scene  // 场景
	RecommendType int64  // 推荐类型
	ElementID     int64  // 干预卡 ID
	ExposureCount int64  // 当日实时曝光量
}
