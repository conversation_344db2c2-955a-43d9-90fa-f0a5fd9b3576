package mrecommendedexposurelevel

// Status 状态
type Status = int8

// Status 状态枚举
const (
	StatusDisabled Status = 0 // 禁用
	StatusEnabled  Status = 1 // 启用
)

// Scene 场景
type Scene = int8

// Scene 场景枚举
const (
	SceneHomePage Scene = 1 // 首页
	SceneLivePage Scene = 2 // 直播页
)

// LivePageGuildRecommendExposureLevelID 直播页直播间推荐曝光等级 ID
const LivePageGuildRecommendExposureLevelID = 8

// Model of m_recommended_exposure_level
type Model struct {
	ID           int64  // 主键
	CreateTime   int64  // 创建时间
	ModifiedTime int64  // 更新时间
	Exposure     int64  // 曝光量，e.g. 100,000
	Level        string // 曝光等级，e.g. S A B C
	Status       Status // 状态，0：禁用，1：启用
	Scene        Scene  // 场景，1：首页，2：直播页
}
