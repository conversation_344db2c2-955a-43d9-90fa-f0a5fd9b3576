package grpc

import (
	"go-common/library/conf/paladin.v2"
	"go-common/library/net/rpc/warden"

	pbv1 "recommend-base/api/v1"
	pbv2 "recommend-base/api/v2"
	svcv1 "recommend-base/internal/service/v1"
	svcv2 "recommend-base/internal/service/v2"
)

// New new a grpc server that supports both v1 and v2 services.
func New(v1 *svcv1.Service, v2 *svcv2.Service) (ws *warden.Server, err error) {
	var (
		cfg warden.ServerConfig
		ct  paladin.TOML
	)
	if err = paladin.Get("grpc.toml").Unmarshal(&ct); err != nil {
		return
	}
	if err = ct.Get("Server").UnmarshalTOML(&cfg); err != nil {
		return
	}
	ws = warden.NewServer(&cfg)

	// 注册 v1 服务
	pbv1.RegisterLiveRoomServer(ws.Server(), v1)

	// 注册 v2 服务
	pbv2.RegisterLiveRoomServer(ws.Server(), v2)
	pbv2.RegisterRecommendServer(ws.Server(), v2)

	ws, err = ws.Start()
	return
}
