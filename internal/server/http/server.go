package http

import (
	"net/http"

	"go-common/library/conf/paladin.v2"
	"go-common/library/log"
	bm "go-common/library/net/http/blademaster"

	pbv1 "recommend-base/api/v1"
	pbv2 "recommend-base/api/v2"
	"recommend-base/internal/model"
	svcv1 "recommend-base/internal/service/v1"
	svcv2 "recommend-base/internal/service/v2"
)

var (
	svcV1 *svcv1.Service
	svcV2 *svcv2.Service
)

// New new a bm server that supports both v1 and v2 API.
func New(v1 *svcv1.Service, v2 *svcv2.Service) (engine *bm.Engine, err error) {
	var (
		cfg bm.ServerConfig
		ct  paladin.TOML
	)
	if err = paladin.Get("http.toml").Unmarshal(&ct); err != nil {
		return
	}
	if err = ct.Get("Server").UnmarshalTOML(&cfg); err != nil {
		return
	}
	svcV1 = v1
	svcV2 = v2
	engine = bm.DefaultServer(&cfg)

	// Register v1 service
	pbv1.RegisterLiveRoomBMServer(engine, v1)

	// Register v2 service
	pbv2.RegisterLiveRoomBMServer(engine, v2)
	pbv2.RegisterRecommendBMServer(engine, v2)

	initRouter(engine)
	err = engine.Start()
	return
}

func initRouter(e *bm.Engine) {
	e.Ping(ping)
	g := e.Group("/recommend-base")
	{
		g.GET("/start", func(c *bm.Context) {
			k := &model.Kratos{
				Hello: "Hello Kratos!",
			}
			c.JSON(k, nil)
		})
	}
}

func ping(ctx *bm.Context) {
	if _, err := svcV2.Ping(ctx, nil); err != nil {
		log.Error("ping error(%v)", err)
		ctx.AbortWithStatus(http.StatusServiceUnavailable)
	}
}
