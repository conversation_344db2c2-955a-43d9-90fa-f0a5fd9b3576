package v1

import (
	"context"

	"go-common/library/conf/paladin.v2"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/google/wire"

	pb "recommend-base/api/v1"
	"recommend-base/internal/dao"
)

var Provider = wire.NewSet(New, wire.Bind(new(pb.LiveRoomServer), new(*Service)))

// Service v1 service.
type Service struct {
	ac  *paladin.Map
	dao dao.Dao
}

// New new a v1 service and return.
func New(d dao.Dao) (s *Service, cf func(), err error) {
	s = &Service{
		ac:  &paladin.TOML{},
		dao: d,
	}
	cf = s.Close
	err = paladin.Watch("application.toml", s.ac)
	return
}

// Ping ping the resource.
func (s *Service) Ping(ctx context.Context, e *empty.Empty) (*empty.Empty, error) {
	return &empty.Empty{}, s.dao.Ping(ctx)
}

// Close close the resource.
func (s *Service) Close() {
}

func (s *Service) GetAllLivingRoomsInfo(ctx context.Context, req *pb.LivingRoomListReq) (resp *pb.AllLivingRoomsInfoResp, err error) {
	roomsInfo, err := s.dao.LivingRoomsInfo(ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}
	if roomsInfo == nil || len(roomsInfo.List) == 0 {
		return &pb.AllLivingRoomsInfoResp{
			List: make([]*pb.AllLivingRoomInfo, 0),
		}, nil
	}

	roomIDs := make([]int64, 0, len(roomsInfo.List))
	for _, roomInfo := range roomsInfo.List {
		roomIDs = append(roomIDs, roomInfo.RoomID)
	}

	roomsExtraInfo, err := s.dao.RoomsExtraInfo(ctx, dao.RoomsExtraInfoParams{RoomIDs: roomIDs})
	if err != nil {
		return nil, err
	}
	resp = new(pb.AllLivingRoomsInfoResp)
	resp.List = make([]*pb.AllLivingRoomInfo, 0, len(roomsInfo.List))
	for _, info := range roomsInfo.List {
		item := &pb.AllLivingRoomInfo{
			Roomid:        info.RoomID,
			Uid:           info.UserID,
			RoomTitle:     info.RoomTitle,
			LiveStartTime: info.LiveStartTime,
			Ctime:         info.CreateTime,
			Score:         info.Score,
		}

		for _, extraInfo := range roomsExtraInfo.List {
			if extraInfo.RoomID == info.RoomID {
				item.CatalogId = extraInfo.CatalogID
				item.SubCatalogId = extraInfo.SubCatalogID
				item.CustomTagId = extraInfo.CustomTagID
				item.CustomTagName = extraInfo.CustomTagName
				item.Online = extraInfo.Online
				item.ActuallyOnline = extraInfo.ActuallyOnline
				item.MessageCount = extraInfo.MessageCount
				item.Board = extraInfo.Board
				break
			}
		}
		resp.List = append(resp.List, item)
	}
	resp.Pagination = &pb.Pagination{
		Maxpage:  roomsInfo.Pagination.MaxPage,
		Count:    roomsInfo.Pagination.Count,
		P:        roomsInfo.Pagination.P,
		Pagesize: roomsInfo.Pagination.PageSize,
	}

	return resp, nil
}
