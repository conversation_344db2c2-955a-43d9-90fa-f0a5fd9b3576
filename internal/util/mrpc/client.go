package mrpc

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go-common/library/log"
)

const CodeRPCSuccess = 0

// Response structure for missevan rpc
type Response struct {
	Code    int             `json:"code"`
	Data    json.RawMessage `json:"data"`
	Message string          `json:"message"`
}

// Config for missevan rpc
type Config struct {
	Endpoint string
	Key      string
}

// Client structure for missevan rpc
type Client struct {
	Config     Config
	httpClient *http.Client
}

// NewClient return the rpc client
func NewClient(conf Config) *Client {
	return &Client{
		Config:     conf,
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}
}

// Do request
func (c *Client) Do(api string, input, output interface{}) error {
	signedData := rpcSign(input, c.Config.Key)
	req, err := http.NewRequest(http.MethodPost, c.Config.Endpoint+api, bytes.NewReader(signedData))
	if err != nil {
		return err
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "recommend-base")
	return c.doRequest(req, output)
}

func (c *Client) doRequest(req *http.Request, output interface{}) error {
	resp, err := c.httpClient.Do(req)
	log.Error("request Header: %v", req.Header)
	log.Error("request %v", req)
	if err != nil {
		return err
	}

	// close response
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var result Response
	err = json.Unmarshal(body, &result)
	if err != nil {
		return err
	}

	if result.Code != CodeRPCSuccess {
		return fmt.Errorf("rpc(%s) status(%d), code(%d) error(%s)", req.URL.Path, resp.StatusCode, result.Code, result.Message)
	}

	if output != nil {
		err = json.Unmarshal(result.Data, output)
		if err != nil {
			return err
		}
	}

	return nil
}

func rpcSign(data interface{}, apiKey string) []byte {
	t := time.Now().Unix()
	var rawData []byte
	switch v := data.(type) {
	case json.Marshaler:
		rawData, _ = v.MarshalJSON()
	default:
		rawData, _ = json.Marshal(data)
	}
	b64Str := base64.StdEncoding.EncodeToString(rawData)

	mac := hmac.New(sha256.New, []byte(apiKey))
	_, _ = mac.Write([]byte(fmt.Sprintf("%s %d", b64Str, t)))
	sign := mac.Sum(nil)
	hexSign := hex.EncodeToString(sign)

	return []byte(fmt.Sprintf("%s %s %d", b64Str, hexSign, t))
}
