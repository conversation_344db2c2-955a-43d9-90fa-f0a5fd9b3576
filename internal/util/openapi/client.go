package openapi

import (
	"context"
	"encoding/json"
	"net/http"
	"net/url"
	"time"

	"go-common/library/net/http/blademaster"
	xtime "go-common/library/time"
)

// Client
type Client struct {
	bmClient *blademaster.Client
	endpoint string
}

// Config
type Config struct {
	Endpoint  string
	AppKey    string
	AppSecret string

	Timeout   string
	KeepAlive string
	Dial      string
}

// NewClient
func NewClient(conf Config) (*Client, error) {
	timeOutDuration, err := time.ParseDuration(conf.Timeout)
	if err != nil {
		return nil, err
	}
	keepAliveDuration, err := time.ParseDuration(conf.KeepAlive)
	if err != nil {
		return nil, err
	}
	dialDuration, err := time.ParseDuration(conf.Dial)
	if err != nil {
		return nil, err
	}

	bmClient := blademaster.NewClient(&blademaster.ClientConfig{
		App: &blademaster.App{
			Key:    conf.AppKey,
			Secret: conf.AppSecret,
		},
		Timeout:   xtime.Duration(timeOutDuration),
		KeepAlive: xtime.Duration(keepAliveDuration),
		Dial:      xtime.Duration(dialDuration),
	})
	return &Client{
		bmClient: bmClient,
		endpoint: conf.Endpoint,
	}, nil
}

func (c *Client) Post(ctx context.Context, api string, params url.Values, res interface{}) error {
	uri := c.endpoint + api
	req, err := c.bmClient.NewRequest(http.MethodPost, uri, "", params)
	if err != nil {
		return err
	}

	err = c.do(ctx, req, res)
	return err
}

func (c *Client) raw(ctx context.Context, req *http.Request, v ...string) (bs []byte, err error) {
	_, bs, err = c.bmClient.RawResponse(ctx, req, v...)
	return
}

func (c *Client) do(ctx context.Context, req *http.Request, res interface{}, v ...string) (err error) {
	var bs []byte
	if bs, err = c.raw(ctx, req, v...); err != nil {
		return
	}
	if res == nil {
		return
	}
	if err = json.Unmarshal(bs, res); err != nil {
		return
	}
	return
}
