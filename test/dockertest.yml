  version: "3.7"
  services:
    db:
      image: mysql:5.6
      ports:
      - 13306:3306
      environment:
      - MYSQL_ROOT_PASSWORD=root
      - TZ=Asia/Shanghai
      - LANG=C.UTF-8
      volumes:
      - .:/docker-entrypoint-initdb.d
      command: [
        '--character-set-server=utf8',
        '--collation-server=utf8_unicode_ci'
      ]
      healthcheck:
        test: ["CMD", "mysqladmin" ,"ping", "--protocol=tcp"]
        timeout: 20s
        interval: 1s
        retries: 20
      hooks:
       - custom: refresh_mysql
    redis:
      image: redis
      ports:
        - 16379:6379
      healthcheck:
        test: ["CMD", "redis-cli","ping"]
        interval: 20s
        timeout: 1s
        retries: 20
      hooks:
       - cmd: ["redis-cli", "flushall"]
    memcached:
      image: memcached:1
      ports:
        - 21211:11211
      healthcheck:
        test: ["CMD", "echo", "stats", "|",  "nc", "127.0.0.1", "11211"]
        interval: 20s
        timeout: 1s
        retries: 20
      hooks:
        - cmd: ["/bin/bash", "-c", "echo 'flush_all' > /dev/tcp/127.0.0.1/11211"]